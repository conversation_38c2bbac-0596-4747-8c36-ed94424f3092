/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复功能，以及高度缓存机制
 *
 * 功能特性：
 * - 简洁的API设计，只传递key和滚动位置
 * - 支持多页面独立管理滚动位置
 * - 内存存储，页面生命周期内有效
 * - 最小侵入性集成
 * - 支持吸顶高度管理，智能处理tab切换滚动逻辑
 * - 支持页面高度缓存，改善tab切换用户体验
 */

// 内存存储滚动位置数据
const scrollPositions = {}

// 内存存储页面高度数据
const pageHeights = {}

// 内存存储吸顶高度配置
let stickyTop = 0

// 内存存储当前滚动位置（用于智能恢复时的临时存储）
let currentScrollPosition = 0

/**
 * 记录滚动位置
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} scrollTop 滚动位置
 */
function setScrollPosition(key, scrollTop) {
  scrollPositions[key] = scrollTop
  setCurrentScrollPosition(scrollTop)
}

/**
 * 智能恢复滚动位置（支持吸顶高度管理）
 * @param {string} key 目标位置标识
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 * @param {string} options.currentKey 当前位置标识（可选，用于获取当前滚动状态）
 */
function restoreScrollPosition(key, options = {}) {
  const { duration = 0, currentKey } = options

  // 获取当前滚动位置
  let currentScroll = getCurrentScrollPosition()
  let stickyTop = getStickyTop()

  const targetSavedScroll = getScrollPosition(key)

  let targetScrollTop = 0

  console.log(key, scrollPositions, currentScroll, targetSavedScroll, stickyTop)

  // 应用滚动高度管理规则
  if (currentScroll < stickyTop) {
    // 规则1：当前tab滚动高度 < 吸顶高度时，下一个tab滚动到当前tab的实际滚动高度
    targetScrollTop = currentScroll
  } else {
    // 规则2：当前tab滚动高度 ≥ 吸顶高度时
    if (targetSavedScroll >= stickyTop) {
      targetScrollTop = targetSavedScroll
    } else {
      // 没有保存的高度：滚动到吸顶高度
      targetScrollTop = stickyTop
    }
  }
  console.log(targetScrollTop, targetSavedScroll)

  wx.pageScrollTo({
    scrollTop: targetScrollTop,
    duration,
  })
}

/**
 * 获取滚动位置
 * @param {string} key 位置标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(key) {
  return scrollPositions[key] || 0
}

/**
 * 设置吸顶高度
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} stickyTop 吸顶高度
 */
function setStickyTop(stickyTop) {
  stickyTop = stickyTop
}

/**
 * 获取吸顶高度
 * @param {string} key 位置标识
 * @returns {number} 吸顶高度
 */
function getStickyTop() {
  return stickyTop || 0
}

/**
 * 设置当前滚动位置（用于智能恢复）
 * @param {number} scrollTop 当前滚动位置
 */
function setCurrentScrollPosition(scrollTop) {
  currentScrollPosition = scrollTop
}

/**
 * 获取当前滚动位置
 * @returns {number} 当前滚动位置
 */
function getCurrentScrollPosition() {
  return currentScrollPosition
}

/**
 * 记录页面高度
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} height 页面高度
 */
function setPageHeight(key, height) {
  pageHeights[key] = height
}

/**
 * 获取页面高度
 * @param {string} key 位置标识
 * @returns {number} 页面高度，如果没有缓存则返回0
 */
function getPageHeight(key) {
  return pageHeights[key] || 0
}

/**
 * 优化的tab切换方法，包含高度缓存机制
 * @param {string} targetKey 目标tab的key
 * @param {Object} pageInstance 页面实例（this）
 * @param {Function} dataUpdateCallback 数据更新回调函数
 * @param {Object} options 选项配置
 * @param {number} options.duration 滚动动画时长，默认0
 */
function optimizedTabSwitch(
  targetKey,
  pageInstance,
  dataUpdateCallback,
  options = {}
) {
  const { duration = 0 } = options

  return new Promise((resolve) => {
    // 第一步：获取目标tab的缓存高度
    const cachedHeight = getPageHeight(targetKey)

    // 第二步：如果有缓存高度，设置为页面最小高度
    if (cachedHeight > 0) {
      pageInstance.setData({
        pageContainerStyle: `min-height: ${cachedHeight}px;`,
      })
    }

    // 第三步：恢复滚动位置
    restoreScrollPosition(targetKey, { duration })

    // 第四步：执行数据更新回调
    Promise.resolve(dataUpdateCallback())
      .then(() => {
        // 第五步：移除最小高度限制
        setTimeout(() => {
          pageInstance.setData({
            pageContainerStyle: "",
          })
          resolve()
        }, duration + 50) // 等待滚动动画完成后再移除高度限制
      })
      .catch(() => {
        // 即使数据更新失败，也要移除高度限制
        pageInstance.setData({
          pageContainerStyle: "",
        })
        resolve()
      })
  })
}

/**
 * 记录当前页面高度（通常在页面滚动或数据更新后调用）
 * @param {string} key 位置标识
 * @param {Object} pageInstance 页面实例（this）
 */
function recordCurrentPageHeight(key, pageInstance) {
  wx.createSelectorQuery()
    .select(".page-container")
    .boundingClientRect((res) => {
      if (res && res.height > 0) {
        setPageHeight(key, res.height)
      }
    })
    .exec()
}

/**
 * 清除滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearScrollPosition(key = null) {
  if (key === null) {
    // 清除所有滚动位置
    Object.keys(scrollPositions).forEach((k) => {
      delete scrollPositions[k]
    })
  } else {
    // 清除指定位置
    delete scrollPositions[key]
  }
}

/**
 * 清除页面高度缓存
 * @param {string} key 位置标识，不传则清除所有
 */
function clearPageHeight(key = null) {
  if (key === null) {
    // 清除所有页面高度
    Object.keys(pageHeights).forEach((k) => {
      delete pageHeights[k]
    })
  } else {
    // 清除指定高度
    delete pageHeights[key]
  }
}

module.exports = {
  setScrollPosition,
  getScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
  setStickyTop,
  setPageHeight,
  getPageHeight,
  optimizedTabSwitch,
  recordCurrentPageHeight,
  clearPageHeight,
}
