# scrollService 高度缓存机制使用指南

## 概述

scrollService 已扩展支持页面高度缓存机制，通过在tab切换时预设页面高度来改善用户体验，防止页面高度突然变化导致的闪烁。

## 新增功能

### 1. 高度缓存API

```javascript
// 记录页面高度
scrollService.setPageHeight(key, height)

// 获取页面高度
const height = scrollService.getPageHeight(key)

// 记录当前页面高度（自动获取）
scrollService.recordCurrentPageHeight(key, pageInstance)

// 清除页面高度缓存
scrollService.clearPageHeight(key) // 清除指定key
scrollService.clearPageHeight()    // 清除所有
```

### 2. 优化的tab切换方法

```javascript
// 一键式优化tab切换
scrollService.optimizedTabSwitch(targetKey, pageInstance, dataUpdateCallback, options)
```

## 使用方式

### 基础集成步骤

#### 1. 导入服务

```javascript
const scrollService = require("@/services/scrollService")
```

#### 2. 修改tab切换方法

**原有方式：**
```javascript
changeTab(e) {
  const index = e.currentTarget.dataset.index
  
  this.setData({ activeIndex: index })
  
  if (index == 1) {
    this.loadTabData()
  } else {
    this.applyFilter()
  }
  
  this.restoreScrollPosition(index)
}
```

**优化后方式：**
```javascript
changeTab(e) {
  const index = e.currentTarget.dataset.index
  const currentIndex = this.data.activeIndex
  
  if (index === currentIndex) return
  
  // 记录当前tab的页面高度
  const currentTabKey = currentIndex.toString()
  scrollService.recordCurrentPageHeight(currentTabKey, this)
  
  this.setData({ activeIndex: index })
  
  // 使用优化的tab切换方法
  const targetTabKey = index.toString()
  const dataUpdateCallback = async () => {
    if (index == 1) {
      await this.loadTabData()
    } else {
      await this.applyFilter()
    }
  }
  
  scrollService.optimizedTabSwitch(targetTabKey, this, dataUpdateCallback, { duration: 0 })
}
```

#### 3. 在滚动事件中记录位置

```javascript
onPageScroll(e) {
  const scrollTop = e.scrollTop
  const { activeIndex } = this.data
  
  // 记录滚动位置到scrollService
  const currentTabKey = activeIndex.toString()
  scrollService.setScrollPosition(currentTabKey, scrollTop)
}
```

#### 4. 在数据加载完成后记录高度

```javascript
async loadData() {
  // 数据加载逻辑
  const res = await this.fetchData()
  
  this.setData({
    dataList: res.data
  })
  
  // 数据更新完成后记录页面高度
  setTimeout(() => {
    const currentTabKey = this.data.activeIndex.toString()
    scrollService.recordCurrentPageHeight(currentTabKey, this)
  }, 100) // 等待DOM更新完成
}
```

### 高级用法

#### 1. 自定义高度记录时机

```javascript
// 在特定业务逻辑完成后记录高度
async handleBusinessLogic() {
  await this.processData()
  
  // 手动记录高度
  const tabKey = this.getCurrentTabKey()
  scrollService.recordCurrentPageHeight(tabKey, this)
}
```

#### 2. 手动设置页面高度

```javascript
// 如果已知页面高度，可以直接设置
const estimatedHeight = 1200
scrollService.setPageHeight('tab-0', estimatedHeight)
```

#### 3. 条件性高度缓存

```javascript
changeTab(e) {
  const index = e.currentTarget.dataset.index
  
  // 只对特定tab启用高度缓存
  if (this.shouldUseHeightCache(index)) {
    // 使用优化方法
    scrollService.optimizedTabSwitch(...)
  } else {
    // 使用传统方法
    this.traditionalTabSwitch(index)
  }
}
```

## 工作原理

### 优化流程

1. **第一步：获取缓存高度**
   - 从 `pageHeights` 对象中获取目标tab的缓存高度

2. **第二步：设置最小高度**
   - 如果有缓存高度，设置 `pageContainerStyle: "min-height: ${height}px;"`

3. **第三步：恢复滚动位置**
   - 调用 `restoreScrollPosition` 恢复目标tab的滚动位置

4. **第四步：更新数据**
   - 执行传入的 `dataUpdateCallback` 函数

5. **第五步：移除高度限制**
   - 数据更新完成后，清除 `pageContainerStyle`，恢复自然高度

### 高度记录时机

- **tab切换前**：记录当前tab的页面高度
- **数据加载后**：记录新数据渲染后的页面高度
- **页面滚动时**：可选择性记录当前高度

## 注意事项

### 1. 页面容器要求

确保页面有正确的容器结构：

```xml
<view class="page-container" style="{{pageContainerStyle}}">
  <!-- 页面内容 -->
</view>
```

### 2. 异步数据处理

数据更新回调函数应该返回Promise：

```javascript
const dataUpdateCallback = async () => {
  await this.loadData()  // 确保数据加载完成
}
```

### 3. 高度记录延迟

DOM更新需要时间，建议使用适当延迟：

```javascript
setTimeout(() => {
  scrollService.recordCurrentPageHeight(key, this)
}, 100) // 100ms通常足够
```

### 4. 内存管理

定期清理不需要的高度缓存：

```javascript
onUnload() {
  // 页面卸载时清理缓存
  scrollService.clearPageHeight()
}
```

## 兼容性

- ✅ 完全兼容现有的 scrollService API
- ✅ 不影响现有的滚动位置管理功能
- ✅ 可以渐进式集成，不需要一次性修改所有页面
- ✅ 支持传统tab切换方式作为降级方案

## 性能优化

- 高度缓存存储在内存中，访问速度快
- 只在必要时记录高度，避免频繁DOM查询
- 支持条件性启用，可根据业务需求选择性使用

## 示例页面

参考 `pages/home/<USER>/index.js` 中的完整实现示例。
