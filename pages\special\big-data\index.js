const ROUTER = require("@/services/mpRouter")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0, // 状态栏高度
    stickyThreshold: 0, // tab滚动吸顶距离
    showFixedHeader: false, // 是否显示固定头部
    isTabSticky: false,

    currentTab: 0,
    scrollLeft: 0,
    tabItemWidth: 0, // 初始化为0，后续获取实际宽度
    containerWidth: 0,

    // 职位数据表格相关
    currentMainTab: "hot", // 热门/冷门 tab
    selectedSubTab: "num", // 默认选中"按报名数"
    currentTableData: [], // 当前表格数据
    showLoadMore: true, // 是否显示查看更多按钮
    displayCount: 10, // 当前显示的数据条数

    jobData: {
      count: 0,
      list: [],
    },
    examTypeOptions: [],
    regionOptions: [],

    // 分页参数
    pageParams: {
      page: 1,
      size: 20,
      hasMore: true, // 是否还有更多数据
      loading: false, // 是否正在加载
    },

    // 是否已经进行过查询（用于判断是否可以触底加载）
    hasSearched: false,

    // vant选择器状态
    optPopShow: false,
    optTitle: "",
    optList: [],
    optKeys: [], // 选项对应的key数组
    value: [],
    defaultIndex: 0, // 默认选中索引
    currentSelector: "", // 当前激活的选择器类型

    // 选择器数据配置
    selectorConfig: {
      examType: {
        title: "选择考试类型",
        options: [],
        currentKey: "",
      },
      region: {
        title: "选择所属地区",
        options: [],
        currentKey: "",
      },
      specificExam: {
        title: "选择具体考试",
        options: [],
        currentKey: "",
      },
      searchUnit: {
        title: "选择用人单位",
        options: [],
        currentKey: "",
      },
      positionLocation: {
        title: "选择地区",
        options: [],
        currentKey: "",
      },
    },

    projectList: [],

    // 当前选中的key值
    selectedExamTypeKey: "",
    selectedRegionKey: "",
    selectedSearchUnitKey: "",
    region_id: "",

    // 显示用的文本值
    selectedExamTypeText: "考试类型",
    selectedRegionText: "所属地区",
    project_name: "重庆事业单位联考",
    region_name: "",

    // 职位输入框的值
    positionInput: "",

    sortIndex: 0,

    // 数据说明展开收起状态
    dataDescExpanded: false,

    // 真实数据
    article_id: null,
    project_id: null,
    region_id: null,
    detailData: {},
    updateTime: null,

    // 用人单位列表
    workList: [],

    // 新增：岗位报考查询地区选择器相关数据
    positionRegionSelector: {
      show: false, // 是否显示选择器
      type: "region", // 选择器类型
      title: "请选择地区", // 选择器标题
      value: [], // 选择器当前选中值（索引数组）
      columns: [], // 选择器列数据
      level: 1, // 当前可编辑级数：1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
      editable: true, // 是否可编辑

      // 显示文本和值
      displayText: "全部地区",
      selectedValues: [], // 存储选中的[省id, 市id, 区id]
      selectedIndexes: [], // 存储选中的每级下标，用于回显：level=1时长度为3，level=2时长度为2，level=3时长度为1

      // 根据level预设的省市信息
      presetProvince: null, // level=2,3,4时的预设省份
      presetCity: null, // level=3,4时的预设城市
      presetDistrict: null, // level=4时的预设区县
    },
    // 新增：地区数据缓存，key为parent_id，value为对应的子级列表
    regionDataCache: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取设备信息
    this.pageOptions = options
    this.scrollMap = {
      list: {},
      top: 0,
    }
    this.getSystemInfo()
    this.getOverView()
  },

  async getOverView(flag) {
    this.setData({
      jobData: { list: [], count: 0 },
    })

    const param = {
      project_id: this.data.project_id || this.pageOptions.project_id,
      article_id: this.data.article_id || this.pageOptions.article_id,
    }

    const res = await UTIL.request(API.getOverView, param)
    if (!res?.data) return

    const {
      detail,
      examtype_list,
      province_list,
      project_list,
      article_list,
      exam_type,
      region_province,
    } = res.data
    const updateTime = detail.last_update_time

    let newState = {
      detailData: detail,
      updateTime,
    }

    // 1. 处理考试类型
    if (examtype_list?.length) {
      const examTypeOptions = examtype_list.map((item) => ({
        text: item.type_name,
        value: item.id,
      }))
      const selectedExam = examTypeOptions.find(
        (item) => item.value == exam_type
      )
      newState.examTypeOptions = examTypeOptions
      if (selectedExam) {
        newState.selectedExamTypeText = selectedExam.text
        newState.selectedExamTypeKey = selectedExam.value
      }
    }

    // 2. 处理地区选项
    if (province_list?.length) {
      const regionOptions = province_list.map((item) => ({
        text: item.area_name,
        value: item.id,
      }))
      const selectedRegion = regionOptions.find(
        (item) => item.value == region_province
      )
      newState.regionOptions = regionOptions
      if (selectedRegion) {
        newState.selectedRegionText = selectedRegion.text
        newState.selectedRegionKey = selectedRegion.value
      }
    }

    // 3. 处理项目信息
    if (project_list?.length) {
      const specificExamOptions = project_list.map((item) => ({
        text: item.name,
        value: item.id,
      }))
      const selectedProject = specificExamOptions.find(
        (item) => item.value == this.pageOptions.project_id
      )
      newState.specificExamOptions = specificExamOptions
      if (selectedProject) {
        newState.project_id = selectedProject.value
        newState.project_name = selectedProject.text
      }
    }

    // 4. 处理文章信息 (仅非切换时)
    let article_id = this.data.article_id
    if (!flag && article_list?.length) {
      const tabList = article_list.map((item) => ({
        name: item.title,
        id: item.id,
      }))

      this.scrollMap.list = tabList.reduce((acc, item) => {
        acc[`${param.project_id}-${item.id}`] = {
          top: 0,
          cachedData: {},
        }
        return acc
      }, {})
      const selectedArticleIndex = tabList.findIndex(
        (item) => item.id == this.pageOptions.article_id
      )

      newState.tabList = tabList
      newState.articleList = article_list

      if (selectedArticleIndex !== -1) {
        article_id = tabList[selectedArticleIndex].id
        newState.currentTab = selectedArticleIndex
      } else {
        article_id = tabList[0].id
        newState.currentTab = 0
      }
      this.getStickyThreshold()
    }
    newState.article_id = article_id

    // 5. 处理地区信息
    if (detail.region_num_list?.length) {
      if (detail.region_num_list.length > 1) {
        detail.region_num_list.unshift({ area_name: "全部", id: "" })
      }
      newState.region_name = detail.region_num_list[0].area_name
      newState.region_id = detail.region_num_list[0].id
    }

    // 6. 设置地区选择器
    await this.setupPositionRegionSelector(detail)

    // 7. 获取用人单位列表
    const workParam = {
      region_province: detail.region_province || "",
      region_city: detail.region_city || "",
      region_district: detail.region_district || "",
      article_id: article_id,
    }

    this.setData(newState)

    await this.getWorkList(workParam)
    await this.getCurrentTableData()
    const {
      workList,
      currentTableData,
      positionRegionSelector,
      regionDataCache,
    } = this.data
    // 8. 缓存数据
    if (
      !(
        this.scrollMap.list[`${param.project_id}-${article_id}`].cachedData &&
        Object.keys(
          this.scrollMap.list[`${param.project_id}-${article_id}`].cachedData
        ).length > 0
      )
    ) {
      this.scrollMap.list[`${param.project_id}-${article_id}`].cachedData = {
        detailData: { ...detail },
        updateTime,
        region_name: newState.region_name || "",
        region_id: newState.region_id || "",
        currentTableData: [...currentTableData],
        workList: [...workList],
        positionRegionSelector: { ...positionRegionSelector },
        regionDataCache: { ...regionDataCache },
      }
    }
  },
  // 获取考试项目列表
  async getProjectList() {
    const param = {
      province: this.data.selectedRegionKey,
      exam_type: this.data.selectedExamTypeKey,
      source: "apply",
    }
    const res = await UTIL.request(API.getProjectList, param)
    if (res) {
      const projectList = res?.data || []
      const project_id = projectList.length ? projectList[0].id : 1
      const project_name = projectList.length ? projectList[0].name : ""
      this.setData({
        projectList,
        project_id,
        project_name,
      })
    }
  },

  // 根据地区获取用人单位
  async getWorkList(param) {
    const res = await UTIL.request(API.getWorkUnitList, param)
    if (res?.data) {
      const workList = [{ work_unit: "全部单位", value: "" }, ...res.data]
      this.setData({ workList })
    }
  },

  /**
   * 获取设备信息
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()

    // 计算导航栏相关高度
    const statusBarHeight = systemInfo.statusBarHeight || 0

    this.setData({
      statusBarHeight: statusBarHeight,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 初始化滚动条
    setTimeout(() => {
      this.initScrollBar()
    }, 100)
  },

  /**
   * 初始化滚动条
   */
  initScrollBar() {
    const query = wx.createSelectorQuery()

    // 先获取容器宽度
    query
      .select(".tab-container")
      .boundingClientRect((containerRect) => {
        if (containerRect) {
          // 再获取tab项宽度
          const itemQuery = wx.createSelectorQuery()
          itemQuery
            .select(".tab-item")
            .boundingClientRect((itemRect) => {
              if (itemRect) {
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: itemRect.width,
                  },
                  () => {
                    this.updateScrollBar()
                  }
                )
              } else {
                // 如果无法获取，使用默认计算值
                const systemInfo = wx.getSystemInfoSync()
                const defaultItemWidth = (180 * systemInfo.windowWidth) / 750 // rpx转px
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: defaultItemWidth,
                  },
                  () => {
                    this.updateScrollBar()
                  }
                )
              }
            })
            .exec()
        }
      })
      .exec()
  },

  /**
   * 更新滚动条位置 - 修复双向滚动问题
   */
  updateScrollBar() {
    const { currentTab, articleList, containerWidth, tabItemWidth } = this.data

    if (!containerWidth || !tabItemWidth) return

    const totalWidth = articleList?.length * tabItemWidth || 0

    // 只有当总宽度大于容器宽度时才需要滚动
    if (totalWidth > containerWidth) {
      // 计算当前tab的中心位置
      const currentTabCenter = currentTab * tabItemWidth + tabItemWidth / 2

      // 计算滚动位置，让当前tab居中
      let targetScrollLeft = currentTabCenter - containerWidth / 2

      // 边界处理
      const maxScrollLeft = totalWidth - containerWidth
      targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

      this.setData({
        scrollLeft: targetScrollLeft,
      })
    }
  },

  /**
   * Tab点击事件
   */
  onTabClick(e) {
    const { index, id } = e.currentTarget.dataset

    this.setData({
      currentTab: index,
      article_id: id,
    })

    const targetTabCache = this.scrollMap.list[`${this.data.project_id}-${id}`]
    // 检查是否有缓存数据
    if (
      targetTabCache?.cachedData &&
      Object.keys(targetTabCache.cachedData).length > 0
    ) {
      // 从缓存中获取数据
      const cachedData = targetTabCache.cachedData
      // 准备要更新的数据对象
      const newData = {
        // 恢复缓存数据
        detailData: { ...cachedData.detailData },
        updateTime: cachedData.updateTime,
        regionName: cachedData.regionName,
        regionId: cachedData.regionId,
        currentTableData: [...cachedData.currentTableData],
        workList: [...cachedData.workList],
        positionRegionSelector: { ...cachedData.positionRegionSelector }, // 对象深拷贝
        regionDataCache: { ...cachedData.regionDataCache },
        // 重置底部职位列表相关数据
        "jobData.count": 0,
        "jobData.list": [],
        hasSearched: false,
        "pageParams.page": 1,
        "pageParams.hasMore": true,
        "pageParams.loading": false,
        positionInput: "",
        selectedSearchUnitKey: "",
      }
      this.setData(newData)
    } else {
      // 没有缓存数据，调用接口获取数据
      this.getOverView(true)
    }

    // 延迟执行滚动，确保数据更新完成
    setTimeout(() => {
      this.updateScrollBar()
    }, 50)
    let pageScrollTop = 0
    const currentTop = this.scrollMap.top
    let height = this.data.stickyThreshold
    const goalTop =
      this.scrollMap.list[`${this.data.project_id}-${id}`]?.top || 0
    if (currentTop < height) {
      pageScrollTop = currentTop
    } else {
      if (goalTop < height) {
        pageScrollTop = height + 1
      } else {
        pageScrollTop = goalTop
      }
    }
    this.scrollMap.list[
      `${this.data.project_id}-${this.data.article_id}`
    ].top = pageScrollTop
    wx.pageScrollTo({
      scrollTop: pageScrollTop,
      duration: 0,
    })
  },

  getStickyThreshold() {
    const query = wx.createSelectorQuery() // 传入组件实例
    query
      .select(".tab-container")
      .boundingClientRect((rect) => {
        this.setData({
          stickyThreshold: rect.top - (this.data.statusBarHeight + 44),
        })
      })
      .exec()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 初始化胶囊按钮颜色
    if (this.data.showFixedHeader) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  /**
   * 页面滚动事件
   */
  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const showFixedHeader = scrollTop > 10 // 滚动超过10rpx显示固定头部
    const isTabSticky = scrollTop > this.data.stickyThreshold

    if (showFixedHeader !== this.data.showFixedHeader) {
      if (!showFixedHeader) {
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      } else {
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
      this.setData({
        showFixedHeader: showFixedHeader,
      })
    }

    if (isTabSticky !== this.data.isTabSticky) {
      this.setData({
        isTabSticky,
      })
    }

    this.scrollMap.top = e.scrollTop
    this.scrollMap.list[`${this.data.project_id}-${this.data.article_id}`].top =
      e.scrollTop
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "分享标题",
      path: "/pages/special/big-data/index",
      imageUrl: "/path/to/share/image.jpg",
    }
  },

  /**
   * 主Tab点击事件（热门/冷门）
   */
  onMainTabClick(e) {
    const index = e.currentTarget?.dataset?.index || e
    this.setData(
      {
        currentMainTab: index,
        displayCount: 10, // 重置显示数量
      },
      () => {
        this.getCurrentTableData()
      }
    )
  },

  /**
   * 子Tab点击事件（按报名数/按竞争比） - 单选模式
   */
  onSubTabClick(e) {
    const index = e.currentTarget.dataset.index
    // 直接设置选中项
    this.setData(
      {
        selectedSubTab: index,
        displayCount: 10, // 重置显示数量
      },
      () => {
        this.getCurrentTableData()
      }
    )
  },

  /**
   * 获取当前表格数据
   */
  async getCurrentTableData() {
    const param = {
      region_id: this.data.region_id,
      project_id: this.data.project_id,
      article_id: this.data.article_id,
      heat: this.data.currentMainTab,
      type: this.data.selectedSubTab,
    }
    const res = await UTIL.request(API.getStatisticsList, param)

    const fullData = res?.data || []
    const result = fullData.slice(0, this.data.displayCount)

    // 更新是否显示"查看更多"按钮
    this.setData({
      showLoadMore: fullData.length > this.data.displayCount,
      currentTableData: result,
    })
  },

  /**
   * 显示选择器
   */
  showSelector(type) {
    const config = this.data.selectorConfig[type]
    if (config) {
      // 将options转换为vant picker需要的格式
      let optList = config.options.map((item) => item.name)
      let optKeys = config.options.map((item) => item.key)

      // 获取当前选中的key并找到对应的索引
      let currentKey = ""
      switch (type) {
        case "examType":
          optList = this.data.examTypeOptions.map((item) => item.text)
          optKeys = this.data.examTypeOptions.map((item) => item.value)
          currentKey = this.data.selectedExamTypeKey
          break
        case "region":
          optList = this.data.regionOptions.map((item) => item.text)
          optKeys = this.data.regionOptions.map((item) => item.value)
          currentKey = this.data.selectedRegionKey
          break
        case "specificExam":
          optList = this.data.specificExamOptions.map((item) => item.text)
          optKeys = this.data.specificExamOptions.map((item) => item.value)
          currentKey = this.data.project_id
          break
        case "searchUnit":
          if (this.data.workList.length) {
            optList = this.data.workList.map((item) => item.work_unit)
            optKeys = this.data.workList.map((item) => item.work_unit)
          }
          currentKey = this.data.selectedSearchUnitKey
          break
        case "positionLocation":
          if (
            this.data.detailData.region_num_list &&
            this.data.detailData.region_num_list.length
          ) {
            optList = this.data.detailData.region_num_list.map(
              (item) => item.area_name
            )
            optKeys = this.data.detailData.region_num_list.map(
              (item) => item.id
            )
          }
          currentKey = this.data.region_id
          break
      }

      // 根据key找到对应的索引
      const currentIndex = optKeys.findIndex((key) => key === currentKey)
      const value = currentIndex >= 0 ? [currentIndex] : [0] // 如果找不到则默认选中第一项

      this.setData({
        currentSelector: type,
        optPopShow: true,
        optTitle: config.title,
        optList: optList,
        optKeys: optKeys, // 保存keys数组
        value: value,
        defaultIndex: currentIndex >= 0 ? currentIndex : 0, // 设置默认选中索引
      })
    }
  },

  /**
   * 考试类型点击事件
   */
  onExamTypeClick() {
    this.showSelector("examType")
  },

  /**
   * 所属地区点击事件
   */
  onRegionClick() {
    this.showSelector("region")
  },

  /**
   * 具体考试点击事件
   */
  onSpecificExamClick() {
    this.showSelector("specificExam")
  },

  /**
   * 搜索单位点击事件
   */
  onSearchUnitClick() {
    this.showSelector("searchUnit")
  },

  /**
   * 职位地区点击事件
   */
  onPositionLocationClick() {
    this.showSelector("positionLocation")
  },

  /**
   * 新增：岗位报考查询地区选择器点击事件
   */
  onPositionRegionClick() {
    // 根据level判断是否可编辑
    if (!this.data.positionRegionSelector.editable) {
      return
    }

    this.showPositionRegionSelector()
  },

  /**
   * 新增：显示岗位报考查询地区选择器
   */
  async showPositionRegionSelector() {
    const { level, selectedIndexes } = this.data.positionRegionSelector
    const { detailData } = this.data

    // 根据level生成对应的列数据
    let columns = []
    let title = "请选择地区"
    let defaultIndex = []

    try {
      switch (level) {
        case 4:
          return

        case 3:
          // level=3: 只能选择区县，直接通过detailData.region_city获取
          title = "请选择区县"
          if (detailData?.region_city) {
            const districts = await this.getRegionDataWithCache(
              detailData.region_city,
              3
            )
            if (districts && districts.length > 0) {
              columns = [{ values: districts }]

              // 使用selectedIndexes进行回显
              if (selectedIndexes && selectedIndexes.length === 1) {
                defaultIndex = [selectedIndexes[0]]
              } else {
                defaultIndex = [0]
              }
            } else {
              wx.showToast({ title: "暂无区县数据", icon: "none" })
              return
            }
          } else {
            wx.showToast({ title: "缺少城市信息", icon: "none" })
            return
          }
          break

        case 2:
          // level=2: 二级联动（市区），第一级通过detailData.region_province获取
          title = "请选择市区"
          if (detailData?.region_province) {
            const cities = await this.getRegionDataWithCache(
              detailData.region_province,
              2
            )
            if (cities && cities.length > 0) {
              // 使用selectedIndexes进行回显，如果没有则使用第一条
              let initialCityIndex = 0
              if (selectedIndexes && selectedIndexes.length === 2) {
                initialCityIndex = selectedIndexes[0]
              }

              // 获取第二级数据（区县）
              const cityId = cities[initialCityIndex]?.value || cities[0]?.value
              const districts = await this.getRegionDataWithCache(cityId, 3)

              let initialDistrictIndex = 0
              if (selectedIndexes && selectedIndexes.length === 2) {
                initialDistrictIndex = selectedIndexes[1]
              }

              columns = [{ values: cities }, { values: districts || [] }]
              defaultIndex = [initialCityIndex, initialDistrictIndex]
            } else {
              wx.showToast({ title: "暂无城市数据", icon: "none" })
              return
            }
          } else {
            wx.showToast({ title: "缺少省份信息", icon: "none" })
            return
          }
          break

        case 1:
        default:
          // level=1: 三级联动，parent_id=0获取第一级
          title = "请选择地区"

          const provinces = await this.getRegionDataWithCache(0, 1)
          if (provinces && provinces.length > 0) {
            // 使用selectedIndexes进行回显，如果没有则使用第一条
            let initialProvinceIndex = 0
            let initialCityIndex = 0
            let initialDistrictIndex = 0

            if (selectedIndexes && selectedIndexes.length === 3) {
              initialProvinceIndex = selectedIndexes[0]
              initialCityIndex = selectedIndexes[1]
              initialDistrictIndex = selectedIndexes[2]
            }

            // 获取第二级数据（城市）
            const provinceId =
              provinces[initialProvinceIndex]?.value || provinces[0]?.value
            const cities = await this.getRegionDataWithCache(provinceId, 2)

            // 获取第三级数据（区县）
            const cityId =
              cities?.[initialCityIndex]?.value || cities?.[0]?.value
            const districts = cityId
              ? await this.getRegionDataWithCache(cityId, 3)
              : []

            columns = [
              { values: provinces },
              { values: cities || [] },
              { values: districts || [] },
            ]
            defaultIndex = [
              initialProvinceIndex,
              initialCityIndex,
              initialDistrictIndex,
            ]
          } else {
            wx.showToast({ title: "暂无省份数据", icon: "none" })
            return
          }
          break
      }

      this.setData({
        "positionRegionSelector.show": true,
        "positionRegionSelector.title": title,
        "positionRegionSelector.columns": columns,
      })

      // 设置默认选中项
      setTimeout(() => {
        const picker = this.selectComponent("#positionRegionPicker")
        if (picker && picker.setIndexes && defaultIndex.length > 0) {
          picker.setIndexes(defaultIndex)
        }
      }, 200)
    } catch (error) {
      console.error("获取地区数据失败:", error)
      wx.showToast({ title: "获取地区数据失败", icon: "none" })
    }
  },

  /**
   * 新增：通过缓存获取地区数据
   */
  async getRegionDataWithCache(parentId, dataLevel = null) {
    // 先检查缓存
    if (
      this.data.regionDataCache.hasOwnProperty(parentId) &&
      this.data.regionDataCache[parentId]
    ) {
      return this.data.regionDataCache[parentId]
    }

    // 缓存中没有，则请求接口
    const data = await this.getRegionData(parentId, dataLevel)

    // 将数据存入缓存
    this.data.regionDataCache[parentId] = data

    return data
  },

  /**
   * 新增：通过接口获取地区数据
   */
  async getRegionData(parentId, dataLevel = null) {
    // 根据parentId和上下文推断应该传递的level值
    let apiLevel = dataLevel || this.getApiLevelByParentId(parentId)

    const param = {
      article_id: this.data.article_id,
      level: apiLevel,
      parent_id: parentId,
    }

    try {
      const res = await UTIL.request(API.getRegionListByArticleId, param)
      if (res && res.data) {
        // 将接口数据转换为选择器需要的格式
        return res.data.map((item) => ({
          text: item.area_name || item.name,
          value: item.id,
          ...item,
        }))
      }
      return []
    } catch (error) {
      console.error("请求地区数据失败:", error)
      return []
    }
  },

  /**
   * 新增：根据parent_id推断应该传递给接口的level值
   */
  getApiLevelByParentId(parentId) {
    // parent_id = 0 表示获取省级数据，接口level = 1
    if (parentId === 0 || parentId === "0") {
      return 1
    }

    // 检查是否在已知的省级数据中
    const provinceData =
      this.data.regionDataCache[0] || this.data.regionDataCache["0"]
    if (provinceData && provinceData.some((item) => item.value == parentId)) {
      // 这是省级id，获取市级数据，接口level = 2
      return 2
    }

    // 检查是否在已知的市级数据中
    for (let cacheKey in this.data.regionDataCache) {
      if (cacheKey !== "0" && cacheKey !== 0) {
        const cacheData = this.data.regionDataCache[cacheKey]
        if (cacheData && cacheData.some((item) => item.value == parentId)) {
          // 这是市级id，获取区级数据，接口level = 3
          return 3
        }
      }
    }

    // 无法推断，默认使用组件的level + 1（因为是获取下一级数据）
    const componentLevel = this.data.positionRegionSelector.level
    return Math.min(componentLevel + 1, 3)
  },

  /**
   * 新增：岗位报考查询地区选择器变化事件
   */
  async onPositionRegionSelectorChange(e) {
    const { picker, value, index } = e.detail
    const { level } = this.data.positionRegionSelector

    try {
      // 根据level处理不同的联动逻辑
      if (level === 1) {
        // 完整三级联动
        if (index === 0) {
          // 省份变化，更新市和区
          const selectedProvince = value[0]
          const cities = await this.getRegionDataWithCache(
            selectedProvince.value,
            2
          )

          // 获取第一个城市的区县数据
          let districts = []
          if (cities && cities.length > 0) {
            districts = await this.getRegionDataWithCache(cities[0].value, 3)
          }

          picker.setColumnValues(1, cities || [])
          picker.setColumnValues(2, districts || [])
        } else if (index === 1) {
          // 城市变化，更新区
          const selectedCity = value[1]
          const districts = await this.getRegionDataWithCache(
            selectedCity.value,
            3
          )

          picker.setColumnValues(2, districts || [])
        }
      } else if (level === 2) {
        // 市区二级联动
        if (index === 0) {
          // 城市变化，更新区
          const selectedCity = value[0]
          const districts = await this.getRegionDataWithCache(
            selectedCity.value,
            3
          )

          picker.setColumnValues(1, districts || [])
        }
      }
      // level=3只有一列，不需要联动处理
    } catch (error) {
      console.error("联动获取地区数据失败:", error)
      wx.showToast({ title: "获取地区数据失败", icon: "none" })
    }
  },

  /**
   * 新增：根据detailData中的level和地区信息设置地区选择器状态
   */
  async setupPositionRegionSelector(data) {
    const detailData = data
    if (!detailData) return

    const level = detailData.level

    let displayText = "全部地区"
    let selectedValues = []
    let editable = true

    // 根据level设置不同的状态
    switch (level) {
      case 4:
        // level=4: 不可编辑，需要显示具体的省-市-区文案
        editable = false

        // 使用接口获取显示文案
        if (
          detailData.region_province &&
          detailData.region_city &&
          detailData.region_district
        ) {
          try {
            // 获取省份数据
            const provinces = await this.getRegionDataWithCache(0, 1)
            const province = provinces?.find(
              (p) => p.value == detailData.region_province
            )

            // 获取城市数据
            const cities = await this.getRegionDataWithCache(
              detailData.region_province,
              2
            )
            const city = cities?.find((c) => c.value == detailData.region_city)

            // 获取区县数据
            const districts = await this.getRegionDataWithCache(
              detailData.region_city,
              3
            )
            const district = districts?.find(
              (d) => d.value == detailData.region_district
            )

            if (province && city && district) {
              displayText = `${province.text}-${city.text}-${district.text}`
            } else {
              displayText = "全部地区"
            }
          } catch (error) {
            console.error("获取地区显示文案失败:", error)
            displayText = "全部地区"
          }
        } else {
          displayText = "全部地区"
        }
        break

      case 3:
        // level=3: 只能编辑区级
        editable = true
        displayText = "全部地区"
        break

      case 2:
        // level=2: 可编辑市区两级
        editable = true
        displayText = "全部地区"
        break

      case 1:
      default:
        // level=1: 完整的三级联动
        editable = true
        displayText = "全部地区"
        break
    }

    this.setData({
      "positionRegionSelector.level": level,
      "positionRegionSelector.editable": editable,
      "positionRegionSelector.displayText": displayText,
      "positionRegionSelector.selectedValues": selectedValues,
      "positionRegionSelector.selectedIndexes": [], // 清空选中索引，确保初始状态正确
      // 清空preset数据，在showPositionRegionSelector中动态设置
      "positionRegionSelector.presetProvince": null,
      "positionRegionSelector.presetCity": null,
      "positionRegionSelector.presetDistrict": null,
    })
  },

  /**
   * 新增：岗位报考查询地区选择器确认事件
   */
  onPositionRegionSelectorConfirm(e) {
    const { value, index } = e.detail
    const presetProvince = this.data.detailData.region_province
    const presetCity = this.data.detailData.region_city
    const presetDistrict = this.data.detailData.region_district
    const { level } = this.data.positionRegionSelector

    let displayText = ""
    let selectedValues = []
    let selectedIndexes = []

    // 根据level处理不同的确认逻辑
    switch (level) {
      case 3:
        // level=3: 只选择了区县，只显示区县名称
        if (value && value.length > 0) {
          const selectedDistrict = value[0]
          displayText = selectedDistrict.text
          selectedValues = [presetProvince, presetCity, selectedDistrict]
          selectedIndexes = [index[0]] // 只保存区县的下标
        }
        break

      case 2:
        // level=2: 选择了市区，只显示市-区
        if (value && value.length >= 2) {
          const selectedCity = value[0]
          const selectedDistrict = value[1]
          displayText = `${selectedCity.text}-${selectedDistrict.text}`
          selectedValues = [presetProvince, selectedCity, selectedDistrict]
          selectedIndexes = [index[0], index[1]] // 保存市和区的下标
        }
        break

      case 1:
      default:
        // level=1: 完整三级联动
        if (value && value.length >= 3) {
          const selectedProvince = value[0]
          const selectedCity = value[1]
          const selectedDistrict = value[2]
          displayText = `${selectedProvince.text}-${selectedCity.text}-${selectedDistrict.text}`
          selectedValues = [selectedProvince, selectedCity, selectedDistrict]
          selectedIndexes = [index[0], index[1], index[2]] // 保存省市区的下标
        }
        break
    }

    const param = {
      region_province: selectedValues[0],
      region_city: selectedValues[1],
      region_district: selectedValues[2],
    }
    this.getWorkList(param)

    // 更新地区选择器状态
    this.setData({
      "positionRegionSelector.show": false,
      "positionRegionSelector.displayText": displayText,
      "positionRegionSelector.selectedValues": selectedValues,
      "positionRegionSelector.selectedIndexes": selectedIndexes,
    })
  },

  /**
   * 新增：岗位报考查询地区选择器取消事件
   */
  onPositionRegionSelectorClose() {
    this.setData({
      "positionRegionSelector.show": false,
    })
  },

  /**
   * 职位输入框输入事件
   */
  onPositionInput(event) {
    const value = event.detail.value

    this.setData({
      positionInput: value,
    })
  },

  onConfirm(event) {
    const { value } = event.detail
    const type = this.data.currentSelector
    const optList = this.data.optList
    const optKeys = this.data.optKeys

    const selectedIndex = optList.findIndex((item) => item == value)
    const selectedKey = optKeys[selectedIndex]
    const selectedText = value

    // 验证选中值是否有效
    if (selectedKey === undefined || !selectedText) {
      console.error("选中值无效:", selectedIndex, optKeys, optList)
      return
    }

    // 根据当前选择器类型更新对应的数据
    const updateData = {
      optPopShow: false,
      currentSelector: "",
      optList: [], // 清空选项列表
      optKeys: [], // 清空keys列表
      value: [], // 清空选中值
      defaultIndex: 0, // 清空默认索引
    }

    switch (type) {
      case "examType":
        updateData.selectedExamTypeKey = selectedKey
        updateData.selectedExamTypeText = selectedText
        break
      case "region":
        updateData.selectedRegionKey = selectedKey
        updateData.selectedRegionText = selectedText
        break
      case "specificExam":
        updateData.project_id = selectedKey
        updateData.project_name = selectedText
        break
      case "searchUnit":
        updateData.selectedSearchUnitKey = selectedKey
        break
      case "positionLocation":
        updateData.region_id = selectedKey
        updateData.region_name = selectedText
        break
      default:
        console.warn("未知的选择器类型:", type)
        return
    }

    this.setData(updateData)
    if (type == "region" || type == "examType") {
      this.getProjectList()
    }
    if (type == "specificExam") {
      this.getOverView()
    }
    if (type == "positionLocation") {
      this.getCurrentTableData()
    }
  },

  /**
   * vant popup关闭
   */
  OptClose() {
    this.setData({
      optPopShow: false,
      currentSelector: "",
      defaultIndex: 0, // 清空默认索引
    })
  },

  /**
   * 返回按钮点击事件
   */
  onBackClick() {
    APP.backPage()
  },

  /**
   * 查看更多按钮点击事件
   */
  onLoadMoreClick() {
    // 增加显示数量
    const newDisplayCount = this.data.displayCount + 10

    this.setData(
      {
        displayCount: newDisplayCount,
      },
      () => {
        // 重新获取表格数据
        this.getCurrentTableData()
      }
    )
  },
  goJobDetail(e) {
    const { id } = e.currentTarget.dataset
    console.log(id, "111111111111")
    ROUTER.navigateTo({
      path: "/pages/job/detail/index",
      query: {
        id,
      },
    })
  },
  goDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
      query: {
        id: this.data.detailData.article_id,
      },
    })
  },
  goFocus() {
    ROUTER.switchTab({
      path: "/pages/my/home/<USER>",
      query: {
        index: 1,
      },
    })
  },
  checkNews() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  goJobs() {
    ROUTER.navigateTo({
      path: "/pages/webview/activityCustomer/index",
      query: {
        event: "zKBr",
      },
    })
  },
  goWeb(e) {
    const query = wx.createSelectorQuery().in(this)
    query.select("#job-search-section").boundingClientRect()
    query.selectViewport().scrollOffset()
    query.select(".fixed-header").boundingClientRect()

    query.exec((res) => {
      if (res && res[0] && res[1] && res[2]) {
        const targetTop = res[0].top
        const scrollTop = res[1].scrollTop
        const headerRect = res[2]

        // 根据当前滚动位置判断头部是否应该显示
        const shouldShowHeader = scrollTop > 10
        let headerHeight = shouldShowHeader ? headerRect.height : 0

        // 如果头部不显示，需要减去状态栏高度 + 导航栏高度
        if (!shouldShowHeader) {
          // 导航栏高度 = 状态栏高度 + 44px（导航栏内容高度）
          headerHeight = this.data.statusBarHeight + 44
        }

        const finalScrollTop = scrollTop + targetTop - headerHeight

        wx.pageScrollTo({
          scrollTop: finalScrollTop,
          duration: 300,
        })
      }
    })
  },

  /**
   * 清除按钮点击事件
   */
  onClearClick() {
    this.setData({
      "positionRegionSelector.selectedValues": [],
      "positionRegionSelector.displayText": "全部地区",
      selectedSearchUnitKey: "",
      positionInput: "",
      jobData: {
        list: [],
        count: 0,
      },
    })
  },

  /**
   * 数据说明展开收起切换
   */
  onToggleDataDesc() {
    const expanded = this.data.dataDescExpanded
    this.setData({
      dataDescExpanded: !expanded,
    })
  },
  changeSort() {
    let sortIndex = this.data.sortIndex
    if (sortIndex === 2) {
      sortIndex = 0
    } else {
      sortIndex += 1
    }
    this.setData({
      sortIndex,
    })

    // 排序变化后重新查询（重置分页）
    this.onSearchClick()
  },

  onPageClick(e) {
    // 获取点击的元素路径
    const path = e.target.dataset.path || ""

    // 如果是点击了图表区域，不处理
    if (e.target.id === "mycharts" || path.includes("ec-canvas")) {
      return
    }

    // 关闭tooltip
    const chart = this.selectComponent("#score-bar-id-multiple")
    if (chart) {
      chart.hideTooltip()
    }
  },
  async getJobList(isLoadMore = false) {
    const { data } = this

    // 如果正在加载，直接返回
    if (data.pageParams.loading) {
      return
    }

    // 如果是加载更多但没有更多数据，直接返回
    if (isLoadMore && !data.pageParams.hasMore) {
      return
    }

    // 设置加载状态
    this.setData({
      "pageParams.loading": true,
    })

    const params = {
      project_id: data.project_id,
      article_id: data.article_id,
      order: data.sortIndex == 1 ? "asc" : "desc",
      work_unit: data.selectedSearchUnitKey,
      keywords: data.positionInput,
      region_province: data.positionRegionSelector.selectedValues[0],
      region_city: data.positionRegionSelector.selectedValues[1],
      region_district: data.positionRegionSelector.selectedValues[2],
      page: data.pageParams.page,
      size: data.pageParams.size,
    }

    try {
      const res = await UTIL.request(API.getJobCompetitiveRateList, params)
      if (res && res.data) {
        const newList = res.data.list || []
        const totalCount = res.data.count || 0

        let updatedList = []
        if (isLoadMore) {
          // 加载更多：追加数据
          updatedList = [...data.jobData.list, ...newList]
        } else {
          // 首次加载：替换数据
          updatedList = newList
        }

        // 判断是否还有更多数据
        const hasMore = updatedList.length < totalCount

        this.setData({
          jobData: {
            count: totalCount,
            list: updatedList,
          },
          "pageParams.hasMore": hasMore,
          "pageParams.page": data.pageParams.page + 1,
        })
      }
    } catch (error) {
      console.error("获取职位列表失败:", error)
    } finally {
      // 取消加载状态
      this.setData({
        "pageParams.loading": false,
      })
    }
  },

  // 锚点定位
  scrollTo(e) {
    const { type } = e.currentTarget.dataset
    if (type == "job" || type == "competitive_rate") {
      if (type == "job") {
        this.onMainTabClick("cold")
      } else {
        this.onMainTabClick("hot")
      }
      this.scrollToElement("position-table-section")
    } else if (type == "work_unit") {
      this.scrollToElement("recruiting-unit")
    }
  },

  scrollToElement(e) {
    const query = wx.createSelectorQuery() // 传入组件实例
    query
      .select(`.${e}`)
      .boundingClientRect((rect) => {
        wx.pageScrollTo({
          scrollTop: rect.top - (this.data.statusBarHeight + 44 + 50),
          duration: 300,
        })
      })
      .exec()
  },

  /**
   * 查询按钮点击事件
   */
  onSearchClick() {
    // 重置分页参数，进行首次查询
    this.setData({
      "pageParams.page": 1,
      "pageParams.hasMore": true,
      "pageParams.loading": false,
      hasSearched: true, // 标记已经进行过查询
    })

    // 调用获取职位列表方法（首次查询）
    this.getJobList(false)
  },

  /**
   * 页面触底加载更多
   */
  onReachBottom() {
    // 只有在已经进行过查询且有数据的情况下才允许加载更多
    if (!this.data.hasSearched || this.data.jobData.list.length === 0) {
      return
    }

    // 触发加载更多
    this.getJobList(true)
  },
})
